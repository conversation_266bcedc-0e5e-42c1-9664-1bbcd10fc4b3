from datetime import datetime
from typing import Optional

import pandas as pd

from ..infra.logger_adapter import get_logger

log = get_logger()


class ScrapAndReworkService:
    """
    PT-BR: Serviço responsável por processar e corrigir eventos relacionados a scrap e rework.
    EN: Service responsible for processing and fixing scrap and rework events.
    """

    def __init__(
        self,
        event_frames_df: pd.DataFrame,
        pc_type: str,
        hourly_data: Optional[pd.DataFrame] = None,
    ) -> None:
        """
        PT-BR: Inicializa o serviço de Scrap and Rework.
        EN: Initializes the Scrap and Rework service.

        :param event_frames_df: DataFrame containing the events to be processed
        :type event_frames_df: pd.DataFrame
        :param pc_type: Tipo de processo (Batch, Continuous, Compounding)
        :type pc_type: str
        :param hourly_data: Optional DataFrame containing hourly data
        :type hourly_data: Optional[pd.DataFrame]
        """
        self._event_frames_df = event_frames_df
        self._pc_type = pc_type
        self._hourly_data = hourly_data
        self._is_continuous = pc_type in {"Continuous", "Continous 2 Products"}
        self._is_compounding = pc_type == "Compounding"

    def fix_duration(self) -> pd.DataFrame:
        """
        PT-BR: Corrige a duração dos eventos relacionados a scrap e rework.
        EN: Fixes the duration of scrap and rework events.

        :return: DataFrame with the duration fixed
        :rtype: pd.DataFrame
        """
        log.info("Fixing Scrap and Rework duration")
        
        scrap_and_rework_events = self._has_scrap_or_rework_events()
        
        if scrap_and_rework_events.empty or not scrap_and_rework_events.any():
            log.info("No scrap or rework events found, skipping duration fix")
            return self._event_frames_df
        
        # PT-BR: Adiciona a coluna scrap_or_rework ao dataframe
        # EN: Adds the scrap_or_rework column to the dataframe
        event_frames_df = self._event_frames_df.copy()
        event_frames_df["scrap_or_rework"] = scrap_and_rework_events
        
        if self._is_continuous:
            return self._fix_duration_continuous(event_frames_df)
        elif self._is_compounding:
            return self._fix_duration_compounding(event_frames_df)
        else:
            return event_frames_df

    def _has_scrap_or_rework_events(self) -> pd.Series:
        """
        PT-BR: Identifica eventos de scrap ou rework no dataframe.
        Verifica nas colunas 'event_code', 'subcat_level1' ou 'metric_code'
        se contêm as palavras 'scrap' ou 'rework' (case-insensitive).
        
        EN: Identifies scrap or rework events in the dataframe.
        Checks in the 'event_code', 'subcat_level1' or 'metric_code' columns
        if they contain the words 'scrap' or 'rework' (case-insensitive).

        :return: Boolean series indicating which lines are scrap or rework events
        :rtype: pd.Series
        """
        if self._event_frames_df.empty:
            return pd.Series(dtype=bool, index=self._event_frames_df.index)

        columns_to_check = ["event_code", "subcat_level1", "metric_code"]
        existing_columns = [
            col for col in columns_to_check if col in self._event_frames_df.columns
        ]

        if not existing_columns:
            return pd.Series(dtype=bool, index=self._event_frames_df.index)

        mask = pd.Series(False, index=self._event_frames_df.index)

        for col in existing_columns:
            col_lower = self._event_frames_df[col].astype(str).str.lower()
            has_scrap = col_lower.str.contains("scrap", na=False)
            has_rework = col_lower.str.contains("rework", na=False)
            mask |= has_scrap | has_rework

        return mask

    def _fix_duration_continuous(self, event_frames_df: pd.DataFrame) -> pd.DataFrame:
        log.info("Not available Service for Scrap and Rework for continuous lines")
        return self._event_frames_df

    def _fix_duration_compounding(self, event_frames_df: pd.DataFrame) -> pd.DataFrame:
        """
        PT-BR: Corrige a duração dos eventos de scrap e rework para linhas compounding.
        EN: Fixes the duration of scrap and rework events for compounding lines.

        :param event_frames_df: DataFrame with events including the scrap_or_rework column
        :type event_frames_df: pd.DataFrame
        :return: DataFrame with the duration fixed
        :rtype: pd.DataFrame
        """
        log.info("Fixing Scrap and Rework duration for COMPOUNDING lines")
        
        event_frames_df = self._prepare_hourly_rate_for_compounding_lines(event_frames_df)
        event_frames_df = self._calculate_total_duration_scrap_and_rework(event_frames_df)

        return event_frames_df

    def _prepare_hourly_rate_for_compounding_lines(
        self, event_frames_df: pd.DataFrame
    ) -> pd.DataFrame:
        """
        PT-BR: Prepara e adiciona as colunas hourly_rate, measurement_unit, running_duration_hourly e mdr_hourly
        aos eventos de scrap/rework baseado nos dados horários (hourly_data).
        EN: Prepares and adds the columns hourly_rate, measurement_unit, running_duration_hourly and mdr_hourly
        to scrap/rework events based on hourly data (hourly_data).

        :param event_frames_df: DataFrame with events including the scrap_or_rework column
        :type event_frames_df: pd.DataFrame
        :return: Enriched DataFrame with hourly_rate, measurement_unit, running_duration_hourly and mdr_hourly columns
        :rtype: pd.DataFrame
        """
        
        enriched_df = event_frames_df.copy()
        enriched_df["hourly_rate"] = pd.NA
        enriched_df["measurement_unit"] = pd.NA
        enriched_df["running_duration_hourly"] = pd.NA
        enriched_df["mdr_hourly"] = pd.NA
        
        if self._hourly_data is None or self._hourly_data.empty:
            log.info("No hourly_data provided; skipping hourly rate enrichment")
            return enriched_df

        hourly_df = self._hourly_data.copy()

        # EN: Normalizes common column names
        if "startDateTime" in hourly_df.columns and "start_time" not in hourly_df.columns:
            hourly_df["start_time"] = pd.to_datetime(hourly_df["startDateTime"])
        if "hourlyRatePerH" in hourly_df.columns and "hourly_rate" not in hourly_df.columns:
            hourly_df["hourly_rate"] = hourly_df["hourlyRatePerH"]
        if (
            "refUnitOfMeasurement" in hourly_df.columns
            and "measurement_unit" not in hourly_df.columns
        ):
            hourly_df["measurement_unit"] = hourly_df["refUnitOfMeasurement"]
        if (
            "runningTimeSeconds" in hourly_df.columns
            and "running_duration" not in hourly_df.columns
        ):
            hourly_df["running_duration"] = hourly_df["runningTimeSeconds"]
        if (
            "running_duration" in hourly_df.columns
            and "running_duration_hourly" not in hourly_df.columns
        ):
            hourly_df["running_duration_hourly"] = hourly_df["running_duration"]
        if "MDR" in hourly_df.columns and "mdr_hourly" not in hourly_df.columns:
            hourly_df["mdr_hourly"] = hourly_df["MDR"]

        required_cols = {"start_time", "hourly_rate", "measurement_unit", "running_duration_hourly", "mdr_hourly"}
        missing_cols = required_cols - set(hourly_df.columns)
        if missing_cols:
            log.info(
                "Hourly data missing required columns %s; skipping hourly enrichment",
                missing_cols,
            )
            return enriched_df

        start_time_series = pd.to_datetime(hourly_df["start_time"])

        if start_time_series.dt.tz is not None:
            start_time_series = start_time_series.dt.tz_convert("UTC")
        hourly_df["hour_start"] = start_time_series.dt.floor("H")
        
        # Filters only scrap or rework events
        scrap_rework_mask = enriched_df["scrap_or_rework"] == True
        scrap_rework_events = enriched_df[scrap_rework_mask].copy()
        
        if not scrap_rework_events.empty:
            original_index = scrap_rework_events.index.copy()
            
            start_time_series = pd.to_datetime(scrap_rework_events["start_time"])

            if start_time_series.dt.tz is not None:
                start_time_series = start_time_series.dt.tz_convert("UTC")
            scrap_rework_events["hour_start"] = start_time_series.dt.floor("H")
            
            # PT-BR: Remove as colunas hourly_rate, measurement_unit, running_duration_hourly e mdr_hourly temporariamente para evitar conflitos no merge
            # EN: Temporarily removes hourly_rate, measurement_unit, running_duration_hourly and mdr_hourly columns to avoid merge conflicts
            columns_to_drop = ["hourly_rate", "measurement_unit", "running_duration_hourly", "mdr_hourly"]
            existing_cols_to_drop = [col for col in columns_to_drop if col in scrap_rework_events.columns]
            scrap_rework_events = scrap_rework_events.drop(columns=existing_cols_to_drop)
            
            # PT-BR: Faz o merge com hourly_data
            # EN: Merges with hourly_data
            scrap_rework_events = scrap_rework_events.merge(
                hourly_df[["hour_start", "hourly_rate", "measurement_unit", "running_duration_hourly", "mdr_hourly"]],
                how="left",
                on="hour_start",
            )
            
            scrap_rework_events.drop(columns=["hour_start"], inplace=True)
            
            # PT-BR: Garante que as colunas existam após o merge
            # EN: Ensures columns exist after merge
            if "hourly_rate" not in scrap_rework_events.columns:
                scrap_rework_events["hourly_rate"] = pd.NA
            if "measurement_unit" not in scrap_rework_events.columns:
                scrap_rework_events["measurement_unit"] = pd.NA
            if "running_duration_hourly" not in scrap_rework_events.columns:
                scrap_rework_events["running_duration_hourly"] = pd.NA
            if "mdr_hourly" not in scrap_rework_events.columns:
                scrap_rework_events["mdr_hourly"] = pd.NA
            
            scrap_rework_events.index = original_index
            
            # PT-BR: Atualiza apenas os eventos de scrap/rework com os valores do hourly_data
            # EN: Updates only scrap/rework events with hourly_data values
            enriched_df.loc[scrap_rework_mask, "hourly_rate"] = scrap_rework_events[
                "hourly_rate"
            ]
            enriched_df.loc[scrap_rework_mask, "measurement_unit"] = scrap_rework_events[
                "measurement_unit"
            ]
            enriched_df.loc[scrap_rework_mask, "running_duration_hourly"] = scrap_rework_events[
                "running_duration_hourly"
            ]
            enriched_df.loc[scrap_rework_mask, "mdr_hourly"] = scrap_rework_events[
                "mdr_hourly"
            ]

        return enriched_df

    def _calculate_total_duration_scrap_and_rework(
        self, event_frames_df: pd.DataFrame
    ) -> pd.DataFrame:
        """
        PT-BR: Calcula e atualiza a duração total dos eventos de scrap e rework.
        Para eventos onde scrap_or_rework == True, atualiza total_duration_seconds:
        - Compounding: (total_duration_seconds * hourly_rate / mdr_hourly)
        - Continuous: Not available
        Também atualiza original_total_duration_seconds com o novo valor.
        
        EN: Calculates and updates the total duration of scrap and rework events.
        For events where scrap_or_rework == True, updates total_duration_seconds:
        - Compounding: (total_duration_seconds * hourly_rate / mdr_hourly)
        - Continuous: Not available
        Also updates original_total_duration_seconds with the new value.

        :param event_frames_df: DataFrame with events including the necessary columns
        :type event_frames_df: pd.DataFrame
        :return: DataFrame with the updated durations
        :rtype: pd.DataFrame
        """
        log.info("Calculating total duration for scrap and rework events")
        
        result_df = event_frames_df.copy()
        
        # PT-BR: Filtra apenas eventos de scrap ou rework
        # EN: Filters only scrap or rework events
        scrap_rework_mask = result_df["scrap_or_rework"] == True
        
        if not scrap_rework_mask.any():
            log.info("No scrap or rework events to process")
            return result_df
        
        # PT-BR: Calcula o novo total_duration_seconds apenas para eventos de scrap/rework
        # EN: Calculates new total_duration_seconds only for scrap/rework events
        scrap_rework_events = result_df[scrap_rework_mask].copy()
        

        if self._is_continuous:
            log.info("Not available Service for Scrap and Rework for continuous lines")
            return result_df
        
        elif self._is_compounding:
            # PT-BR: Para compounding: usa hourly_rate e mdr_hourly
            # EN: For compounding: uses hourly_rate and mdr_hourly
            required_cols = ["total_duration_seconds", "hourly_rate", "mdr_hourly"]
            missing_cols = [col for col in required_cols if col not in result_df.columns]
            if missing_cols:
                log.warning(
                    f"Missing required columns {missing_cols} for compounding duration calculation; skipping"
                )
                return result_df
            
            # PT-BR: Evita divisão por zero ou valores nulos
            # EN: Avoids division by zero or null values
            valid_mask = (
                scrap_rework_events["mdr_hourly"].notna()
                & (scrap_rework_events["mdr_hourly"] != 0)
                & scrap_rework_events["hourly_rate"].notna()
                & scrap_rework_events["total_duration_seconds"].notna()
            )
            
            if valid_mask.any():
                # PT-BR: Calcula a nova duração: (total_duration_seconds * hourly_rate / mdr_hourly)
                # EN: Calculates new duration: (total_duration_seconds * hourly_rate / mdr_hourly)
                new_duration = (
                    scrap_rework_events.loc[valid_mask, "total_duration_seconds"]
                    * scrap_rework_events.loc[valid_mask, "hourly_rate"]
                    / scrap_rework_events.loc[valid_mask, "mdr_hourly"]
                ).astype("float64")
                

                result_df.loc[scrap_rework_mask & valid_mask, "total_duration_seconds"] = new_duration
                
                # PT-BR: Atualiza original_total_duration_seconds com o novo valor
                # EN: Updates original_total_duration_seconds with the new value
                if "original_total_duration_seconds" not in result_df.columns:
                    result_df["original_total_duration_seconds"] = result_df["total_duration_seconds"]
                else:
                    result_df.loc[
                        scrap_rework_mask & valid_mask, "original_total_duration_seconds"
                    ] = new_duration
                
                log.info(
                    f"Updated duration for {valid_mask.sum()} compounding scrap/rework events"
                )
            else:
                log.warning(
                    "No valid data (mdr_hourly, hourly_rate, total_duration_seconds) found for compounding duration calculation"
                )
        else:
            log.info("Process type is neither continuous nor compounding; skipping duration calculation")
        
        return result_df

    def fix_start_end_time(
        self,
        event_frames_df: pd.DataFrame,
        shifts: dict,
    ) -> pd.DataFrame:
        """
        PT-BR: Ajusta os horários de início e fim dos eventos de scrap e rework
        para alinhar com os turnos de operação.
        
        EN: Adjusts the start and end times of scrap and rework events
        to align with operational shifts.

        :param event_frames_df: DataFrame with events including the scrap_or_rework column
        :type event_frames_df: pd.DataFrame
        :param shifts: Dictionary defining the operational shifts
        :type shifts: dict
        :return: DataFrame with adjusted start and end times
        :rtype: pd.DataFrame
        """
        log.info("Fixing start and end times for scrap and rework events based on shifts")
        
        adjusted_df = event_frames_df.copy()
        
        # PT-BR: Filtra apenas eventos de scrap ou rework
        # EN: Filters only scrap or rework events
        scrap_rework_mask = adjusted_df["scrap_or_rework"] == True
        
        if not scrap_rework_mask.any():
            log.info("No scrap or rework events to adjust")
            return adjusted_df
        
        scrap_rework_events = adjusted_df[scrap_rework_mask].copy()
        
        # PT-BR: Processa os turnos
        # EN: Process shifts
        shifts = sorted(datetime.strptime(ts, "%Y-%m-%d %H:%M:%S") for ts in shifts)
        midnight = min(shifts, key=lambda dt: dt.time())
        shifts.remove(midnight)
        shift_times = [dt.time() for dt in shifts]

        def get_shift_aligned_time(
            timestamps: pd.Series,
            shift_times: list,
            is_start: bool = True,
        ) -> pd.Series:
            """
            PT-BR: Função interna que alinha timestamps aos horários de turnos.
            Para horários de início: encontra o turno mais recente <= timestamp
            Para horários de fim: encontra o próximo turno >= timestamp
            
            EN: Internal function that aligns timestamps to shift times.
            For start times: finds the most recent shift <= timestamp
            For end times: finds the next shift >= timestamp
            """
            aligned_times = []
            for ts in timestamps:
                ts_time = ts.time()
                
                if is_start:
                    possible_shifts = [st for st in shift_times if st <= ts_time]
                    if possible_shifts:
                        aligned_time = max(possible_shifts)
                    else:
                        aligned_time = shift_times[-1]
                else:
                    possible_shifts = [st for st in shift_times if st >= ts_time]
                    if possible_shifts:
                        aligned_time = min(possible_shifts)
                    else:
                        aligned_time = shift_times[0]
                
                aligned_datetime = pd.Timestamp.combine(ts.date(), aligned_time).tz_localize(tz=timestamps.dt.tz)
                aligned_times.append(aligned_datetime)
                
            return pd.Series(aligned_times, index=timestamps.index)

        scrap_rework_events["start_time"] = get_shift_aligned_time(
            scrap_rework_events["start_time"], shift_times
        )
        scrap_rework_events["end_time"] = get_shift_aligned_time(
            scrap_rework_events["end_time"], shift_times, is_start=False
        )
        
        # PT-BR: Agrupa eventos que agora têm o mesmo horário de início, fim e definição e soma as durações de eventos
        # EN: Groups events that now have the same start time, end time and definition and sums durations
        scrap_rework_events = scrap_rework_events.groupby(["start_time", "end_time", "event_definition"]).agg({
            "total_duration_seconds": "sum", 
            **{col: "first" for col in scrap_rework_events.columns if col not in ["start_time", "end_time", "event_definition", "total_duration_seconds"]}
        }).reset_index()
        
        scrap_rework_events = scrap_rework_events[scrap_rework_events["total_duration_seconds"] != 0]
        
        # PT-BR: Atualiza os eventos no DataFrame original com os novos horários alinhados
        # EN: Updates the original DataFrame events with the new aligned times
        adjusted_df = adjusted_df[~scrap_rework_mask]
        adjusted_df = pd.concat([adjusted_df, scrap_rework_events], ignore_index=True)
        
        return adjusted_df