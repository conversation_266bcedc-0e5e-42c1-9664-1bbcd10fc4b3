METRIC_CODE_TO_LOSS_CATEGORY: dict[str, str] = {
    "Loading": "Commercial",
    "Quality": "Plant Controllable",
    "Performance": "Plant Controllable",
}

TIME_SERIES_EXTERNAL_ID_TO_UNIT: dict[str, str] = {
    "WASIP21MPW:s=opcuaMPW:RAW.PMCAnalogDef.1290-0160-WC": "lbs/h",
    "WASIP21MPW:s=opcuaMPW:RAW.PMCAnalogDef.Z1-TOTAL-PROD": "kg",
    "WASIP21MPW:s=opcuaMPW:RAW.PMCAnalogDef.Z1-PLOP-TOTAL": "kg",
    "WASIP21MPW:s=opcuaMPW:RAW.PMCAnalogDef.Z1-DIVERT-TOTAL": "kg",
    "WASIP21MPW:s=opcuaMPW:RAW.PMCAnalogDef.1290-2213-WC": "lbs/h",
    "WASIP21MPW:s=opcuaMPW:RAW.PMCAnalogDef.Z2-TOTAL-PROD": "kg",
    "WASIP21MPW:s=opcuaMPW:RAW.PMCAnalogDef.Z2-PLOP-TOTAL": "kg",
    "WASIP21MPW:s=opcuaMPW:RAW.PMCAnalogDef.Z2-DIVERT-TOTAL": "kg",
}
