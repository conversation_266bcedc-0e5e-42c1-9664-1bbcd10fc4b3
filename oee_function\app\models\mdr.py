from datetime import datetime
from typing import Any, Optional

from pydantic import Field, computed_field

from .business_segment import BusinessSegment
from .country import Country
from .node import Node
from .region import Region
from .reporting_line import ReportingLine
from .reporting_site import ReportingSite
from .unit_of_measurement import UnitOfMeasurement
from .material import Material


class Mdr(Node):
    reporting_site: ReportingSite = Field(alias="refSite")
    reporting_line: ReportingLine = Field(alias="refReportingLine")
    refMaterial: Optional[Material] = Field(default=None, alias="refMaterial")
    date_set: str = Field(default=None, alias="dateSet")
    uom: UnitOfMeasurement = Field(alias="refUnitOfMeasurement")
    product_family: Optional[str] = Field(default=None, alias="productFamily")
    product: str = Field(default=None, alias="product")
    unit_avg_rate: float = Field(default=None, alias="unitAvgRate")
    pi_tag_value: Optional[str] = Field(default=None, alias="piTagValue")
    scheduled_rate: Optional[float] = Field(default=None, alias="scheduledRate")

    @computed_field
    @property
    def date_set_datetime(self) -> datetime:
        return datetime.fromisoformat(self.date_set)

    @computed_field
    @property
    def month(self) -> int:
        return self.date_set_datetime.month

    @computed_field
    @property
    def year(self) -> int:
        return self.date_set_datetime.year

    @computed_field
    @property
    def timestamp(self) -> datetime:
        return datetime(self.year, self.month, 1)

    @classmethod
    def from_cognite_response(cls, item: dict[str, Any]) -> "Mdr":
        return Mdr(
            refSite=ReportingSite(**item["refSite"]),
            externalId=item["externalId"],
            space=item["space"],
            refReportingLine=ReportingLine(**item["refReportingLine"]),
            unitAvgRate=item["unitAvgRate"],
            dateSet=item["dateSet"],
            productFamily=item["productFamily"],
            piTagValue=item["piTagValue"],
            refUnitOfMeasurement=UnitOfMeasurement(**item["refUnitOfMeasurement"]),
            scheduledRate=item["scheduledRate"],
            refMaterial=(
                Material(**item["refMaterial"]) if item["refMaterial"] else None
            ),
        )
