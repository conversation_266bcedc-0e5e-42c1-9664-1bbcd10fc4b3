class Constants:
    RLT = "rlt"
    PRODUCT = "Product"
    PRODUCT_DESCRIPTION = "ProductDescription"
    RLT_NO_DEMAND = "rlt_no_demand"
    RUNNING_TIME = "running_time"
    TOTAL_PRODUCED = "NetProduction"
    MSDP = "msdp"
    SCHEDULED_RATE = "scheduledRate"
    TOLERANCE = 0.001
    LINES_WITH_MDR_EQUALS_FOR_ALL_PRODUCTS = {
        "RLN-FORPARD01",
        "RLN-FORPARD02",
    }

    LINES_USE_SCHEDULED_RATE_FOR_RLT = [
        "RLN-BISCHMMO3",
        "RLN-BISCHMMO4",
        "RLN-<PERSON><PERSON>KAANAA<PERSON>",
        "RLN-<PERSON><PERSON>KAASAAS",
        "RLN-FRAEUS3G8",
        "RLN-FRAVAMACH",
        "RLN-FRAEUSCRH",
        "RLN-NANHACH<PERSON>",
        "RLN-<PERSON>ISPOMSHY",
        "RLN-FRACHMPM1",
        "RLN-FRACHMPM2",
        "RLN-<PERSON>IS<PERSON>URG<PERSON>",
        "RLN-NANGURG<PERSON>",
    ]
    
    LINES_WITH_MDRTAG = [
        "RLN-NANCELLN1",
        "RLN-NANCELLN2",
        "RLN-NANCMPLN3",
        "RLN-NANCMPLN4",
        "RLN-NANCMPLN5",
        "RLN-NANCMPLN6",
        "RLN-NANCMPLN7",
        "RLN-NANCMPLN9",
    ]


class EventFrameConstants:
    PRODUCT = "Product"
    PRODUCT_DESCRIPTION = "ProductDescription"
    BATCH_ID = "BatchID"
    PROCESS_ORDER = "ProcessOrder"
    MDR = "MDR"
    TOTAL_FEED_UNIT = "total_feed_unit"
    LINES_BY_PASS_COMPOUNDING = {
        "RLN-FORPARD01",
        "RLN-FORPARD02",
    }
    BATCH_IDLE = "Batch Idle"

    # DISABLE SITES
    # Manual OEE => User Story: https://dev.azure.com/CelaneseCorporation/Digital%20Plant/_workitems/edit/203062
    IGNORE_SITES = [
        "STS-CAN",  # Cangrejera
        "STS-BCH",  # Boucherville
        "STS-EVA",  # Evansville
        "STS-FLO",  # Florence
        "STS-NPT",  # Newport
        "STS-OBH",  # Oberhausen
        "STS-PEN",  # Pensacola
        "STS-PER",  # Perstorp
        "STS-SHY",  # Shelby
        "STS-SPS",  # Singapore
        "STS-SHE",  # Songjang
        "STS-UTZ",  # Utzenfeld
        "STS-WIL",  # Wilmington
        "STS-WIN",  # Winona
    ]

    # DISABLE LINES
    IGNORE_LINES = [
        "RLN-FRACNV108",
        "RLN-FRACNV118",
        "RLN-NARFLKD09",
        "RLN-BISCMP517",
        "RLN-BISCMP518",
        "RLN-FORTPEA01",  # Bug v2 208475: [FOR] [TPE] Discontinue A1, A2, A3, A4, A5 and B1 Lines from the Code
        "RLN-FORTPEA02",  # Bug v2 208475: [FOR] [TPE] Discontinue A1, A2, A3, A4, A5 and B1 Lines from the Code
        "RLN-FORTPEA03",  # Bug v2 208475: [FOR] [TPE] Discontinue A1, A2, A3, A4, A5 and B1 Lines from the Code
        "RLN-FORTPEA04",  # Bug v2 208475: [FOR] [TPE] Discontinue A1, A2, A3, A4, A5 and B1 Lines from the Code
        "RLN-FORTPEA05",  # Bug v2 208475: [FOR] [TPE] Discontinue A1, A2, A3, A4, A5 and B1 Lines from the Code
        "RLN-FORTPEB01",  # Bug v2 208475: [FOR] [TPE] Discontinue A1, A2, A3, A4, A5 and B1 Lines from the Code
        "RLN-WASEPCCP6",  # Line under development
        "RLN-WASEPCCP7",  # Line under development
        "RLN-WASEPCCP8",  # Line under development
        "RLN-WASEPCCP9",  # Line under development
        "RLN-WASMPWZ01",  # Line under development
        "RLN-WASMPWZ02",  # Line under development
    ]

    # Lines that require product filtering for MSDP lookup
    HAS_PRODUCT_FILTER = [
        "RLN-BISPOMSHY",
        "RLN-FRACHMPM1",
        "RLN-FRACHMPM2",
        "RLN-NANGURGUR",
    ]

    UNITS_WITH_SCRAP_OR_REWORK = [
        "UNT-BISCMP",
        "UNT-NANCEL"
    ]
