from typing import Any, Optional

import pandas as pd
from cognite.client.data_classes.data_modeling import (
    EdgeApply,
    NodeApply,
    ViewId,
)
from pydantic import Field

from .event_hierarchy_configuration import EventHierarchyConfiguration
from .input_tag_configuration import InputTagConfiguration
from .node import Node
from .reporting_line import ReportingLine
from .reporting_site import ReportingSite


class ReportingSiteConfiguration(Node):
    reporting_site: ReportingSite = Field(alias="reportingSite")
    shifts: list[str] = Field(default=[], alias="shifts")
    regex_processing: Optional[str] = Field(
        default=None, alias="regexProcessing"
    )
    extra_event_processing: Optional[str] = Field(
        default=None, alias="extraEventProcessing"
    )
    extra_time_processing: Optional[int] = Field(
        default=None, alias="extraTimeProcessing"
    )
    input_tags: list[InputTagConfiguration] = Field(
        default_factory=list, alias="inputTags", exclude=True
    )
    events_hierarchy: list[EventHierarchyConfiguration] = Field(
        default_factory=list, alias="eventsHierarchy", exclude=True
    )
    fix_event_no_val: Optional[bool] = Field(
        default=None, alias="fixEventNoVal"
    )
    line_type: Optional[str] = Field(default=None, alias="lineType")
    fix_null_values_subset: list[str] = Field(
        default=[], alias="fixNullValuesSubset"
    )

    def get_minor_stop_configuration(
        self, reporting_line: str
    ) -> Optional[list[tuple[str, str]]]:
        minor_stop_events = []
        for event in self.events_hierarchy:
            if (
                not event.minor_stop
                or not event.event_hierarchy
                or not event.new_minor_stop
                or not event.reporting_line.external_id == reporting_line
            ):
                continue
            minor_stop_events.append(
                (event.event_hierarchy, event.new_minor_stop)
            )
        return (
            None
            if len(minor_stop_events) == 0
            else list(set(minor_stop_events))
        )

    def get_not_running_configuration(
        self, reporting_line: str
    ) -> Optional[list[tuple[str, str]]]:
        not_running_events = []
        for event in self.events_hierarchy:
            if (
                not event.not_running_rule
                or not event.event_hierarchy
                or not event.new_not_running
                or not event.reporting_line.external_id == reporting_line
            ):
                continue
            not_running_events.append(
                (event.event_hierarchy, event.new_not_running)
            )
        return (
            None
            if len(not_running_events) == 0
            else list(set(not_running_events))
        )

    def get_ingestions_extra_props(
        self, reporting_line_external_id: str
    ) -> dict[str, Optional[str]]:
        reporting_line: Optional[ReportingLine] = None
        for input_tag in self.input_tags:
            if (
                input_tag.reporting_line.external_id
                == reporting_line_external_id
            ):
                reporting_line = input_tag.reporting_line
                break
        if not reporting_line:
            return {}

        site = self.reporting_site
        country = site.country
        region = country.parent if country else None
        unit = (
            reporting_line.reporting_unit
            if reporting_line.reporting_unit
            else None
        )
        business_segment = (
            reporting_line.legacy_business_line.maps_to
            if reporting_line.legacy_business_line
            and reporting_line.legacy_business_line.maps_to
            else None
        )

        process_type = reporting_line.process_type

        return {
            "refRegionId": region.external_id if region else None,
            "refCountryId": country.external_id if country else None,
            "refSiteId": site.external_id,
            "refUnitId": unit.external_id if unit else None,
            "refBusinessSegmentId": business_segment.external_id
            if business_segment
            else None,
            "refReportingLineId": (
                reporting_line.external_id if reporting_line else None
            ),
            "refReportingLineName": reporting_line.name
            if reporting_line
            else None,
            "refOEEProductId": None,
            "refProcessTypeId": process_type.external_id
            if process_type
            else None,
            "lineType": self.line_type or None,
        }

    def _dependends_on_bbct_mapper(self) -> dict[str, dict[str, bool]]:
        result: dict[str, dict[str, bool]] = {}
        for event in self.events_hierarchy:
            if not event.event_hierarchy:
                continue
            event.reporting_line.external_id
            entry = result.get(event.reporting_line.external_id, {})
            entry[event.event_hierarchy] = event.uses_bbct or False
            result[event.reporting_line.external_id] = entry
        return result

    def get_shifts_as_timestamp(self):
        return [pd.Timestamp(shift) for shift in self.shifts]

    def event_dependends_on_bbct(
        self, reporting_line_external_id: str, event_hierarchy: str
    ) -> bool:
        return (
            self._dependends_on_bbct_mapper()
            .get(reporting_line_external_id, {})
            .get(event_hierarchy, False)
        )

    def get_extra_processing_params(self) -> Optional[tuple[str, int]]:
        return (
            (
                self.extra_event_processing,
                self.extra_time_processing,
            )
            if self.extra_event_processing and self.extra_time_processing
            else None
        )

    def get_tag_event_identification(
        self, reporting_line_external_id: str
    ) -> list[str]:
        input_tags = []
        for input_tag in self.input_tags:
            if (
                input_tag.reporting_line.external_id
                != reporting_line_external_id
            ):
                continue

            if not input_tag.event_identification:
                continue

            input_tags.append(input_tag.alias)

        return input_tags

    def get_events_hierarchy_by_reporting_line(
        self, reporting_line_external_id: str
    ):
        return [
            event
            for event in self.events_hierarchy
            if event.reporting_line
            and event.reporting_line.external_id == reporting_line_external_id
        ]

    def get_reporting_unit_by_reporting_line(self):
        for input_tag in self.input_tags:
            if input_tag.reporting_line.reporting_unit:
                return input_tag.reporting_line.reporting_unit.external_id
        return None

    def get_tag_configuration_by_reporting_line(self):
        result: dict[str, list[InputTagConfiguration]] = {}
        for input_tag in self.input_tags:
            entry = result.get(input_tag.reporting_line.external_id, [])
            entry.append(input_tag)
            result[input_tag.reporting_line.external_id] = entry
        return result

    def get_timezone(self):
        return (
            self.reporting_site.time_zone.name or "UTC"
            if self.reporting_site and self.reporting_site.time_zone
            else "UTC"
        )

    def get_timeseries(self, reporting_line: str):
        return set(
            [
                input_tag.time_series
                for input_tag in self.input_tags
                if input_tag.reporting_line.external_id == reporting_line
                and input_tag.time_series != ''
            ]
        )

    @classmethod
    def from_cognite_response(
        cls, item: dict[str, Any]
    ) -> "ReportingSiteConfiguration":
        return cls(
            externalId=item["externalId"],
            space=item["space"],
            reportingSite=ReportingSite(**item["reportingSite"]),
            eventsHierarchy=[
                EventHierarchyConfiguration.from_cognite_response(entry)
                for entry in item["eventsHierarchy"]["items"]
                if entry
            ],
            inputTags=[
                InputTagConfiguration.from_cdf_response(entry)
                for entry in item["inputTags"]["items"]
                if entry
            ],
            shifts=item["shifts"],
            regexProcessing=item["regexProcessing"],
            extraEventProcessing=item["extraEventProcessing"],
            extraTimeProcessing=item["extraTimeProcessing"],
            fixEventNoVal=item["fixEventNoVal"],
            lineType=item["lineType"],
            fixNullValuesSubset=item["fixNullValuesSubset"],
        )

    def convert_to_nodes_and_edges(
        self,
        reporting_site_configuration_view: ViewId,
        input_tag_configuration_view: ViewId,
        event_hierarchy_configuration_view: ViewId,
    ) -> tuple[list[NodeApply], list[EdgeApply]]:
        edges: list[EdgeApply] = []
        nodes: list[NodeApply] = []

        nodes.append(
            self.convert_to_cognite_node(
                reporting_site_configuration_view,
            )
        )
        for input_tag in self.input_tags:
            edges.append(
                self.convert_to_cognite_edge(
                    reporting_site_configuration_view, "inputTags", input_tag
                )
            )

            nodes.append(
                input_tag.convert_to_cognite_node(input_tag_configuration_view)
            )
        for event_hierarchy in self.events_hierarchy:
            edges.append(
                self.convert_to_cognite_edge(
                    reporting_site_configuration_view,
                    "eventsHierarchy",
                    event_hierarchy,
                )
            )
            nodes.append(
                event_hierarchy.convert_to_cognite_node(
                    event_hierarchy_configuration_view
                )
            )

        return (nodes, edges)