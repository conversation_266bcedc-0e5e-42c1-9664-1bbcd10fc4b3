from typing import Any, Optional

import pandas as pd
from cognite.client import Cognite<PERSON><PERSON>
from cognite.client.data_classes.data_modeling.data_models import DataModelId

from ..models.mdr import Mdr
from .view_repository import ViewRepository


class MdrRepository:
    def __init__(
        self,
        cognite_client: CogniteClient,
        data_model_id: DataModelId,
        view_repository: Optional[ViewRepository] = None,
    ) -> None:
        self._cognite_client = cognite_client
        self._data_model_id = data_model_id
        self._view_repository = view_repository

    def _get_mdr_data(self, reporting_line_external_ids: list[str]):
        query_result = self._cognite_client.data_modeling.graphql.query(
            self._data_model_id,
            self.build_query(),
            {"reportingLines": reporting_line_external_ids},
        )["listMaximumDemonstratedRate"]["items"]

        if not query_result:
            return None

        return [
            Mdr.from_cognite_response(entry)
            for entry in query_result
            if entry["refReportingLine"]
        ]

    def get_mdr_data_as_dataframe(
        self, reporting_line_external_ids: list[str]
    ) -> pd.DataFrame:
        data = self._get_mdr_data(reporting_line_external_ids)

        entries: list[dict[str, Any]] = []
        if data is None:
            return None
        else:
            for item in data:
                entry = item.model_dump(by_alias=True)
                entry.update(
                    {
                        "reportingLineExternalId": item.reporting_line.external_id,
                        "reportingLineName": item.reporting_line.name,
                        "productId": item.pi_tag_value,
                    }
                )
                entries.append(entry)
            table_raw = pd.DataFrame(entries)

            table_raw = table_raw.drop(columns=["refReportingLine"])

        return table_raw

    def build_query(self):
        return """
            query QueryMdr($reportingLines: [ID!]) {
                listMaximumDemonstratedRate(
                first: 1000
                filter: {refReportingLine: {externalId: {in: $reportingLines}}}
                sort: {dateSet: DESC}
                ) {
                    items {
                        externalId
                        space
                        dateSet
                        productFamily
                        unitAvgRate
                        piTagValue
                        scheduledRate
                        refMaterial {
                            externalId
                            space
                            name
                            description
                        }
                        refSite {
                            externalId
                            space
                        }
                        refReportingLine {
                            name
                            externalId
                            space
                        }
                        refUnitOfMeasurement {
                            name
                            externalId
                            space
                            symbol
                        }
                    }
                }
            }
            """

    def create_mdr(self, mdrs: list[Mdr]) -> None:
        if not mdrs:
            return

        if not self._view_repository:
            raise ValueError(
                "Missing View Repository when instantiating the MDR Repository."
            )

        view = self._view_repository.get_view_id("MaximumDemonstratedRate")

        keys_to_exclude = [
            "date_set_datetime",
            "month",
            "year",
            "timestamp",
            "index",
            "reportingLineExternalId",
            "reportingSiteExternalId",
            "businessSegmentExternalId",
            "uomExternalId",
            "reportingUnitExternalId",
            "regionExternalId",
            "countryExternalId",
            "reportingLineSpace",
            "reportingSiteSpace",
            "regionSpace",
            "countrySpace",
            "businessSegmentSpace",
            "uomSpace",
            "reportingUnitSpace",
            "refOEEProduct",
        ]
        nodes = []
        for mdr in mdrs:
            nodes.append(
                mdr.convert_to_cognite_node(
                    view, keys_to_exclude=keys_to_exclude
                )
            )

        paginated_nodes = [
            nodes[1000 * i : 1000 * (i + 1)]
            for i in range(int(len(nodes) / 1000) + 1)
        ]

        for entries in paginated_nodes:
            self._cognite_client.data_modeling.instances.apply(nodes=entries)
