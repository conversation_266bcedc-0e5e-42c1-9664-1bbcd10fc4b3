from typing import Any, Optional

import pandas as pd
from cognite.client import CogniteClient
from cognite.client.data_classes.data_modeling.data_models import DataModelId

from ..models.msdp import Msdp
from .view_repository import ViewRepository


class MsdpRepository:
    def __init__(
        self,
        cognite_client: CogniteClient,
        data_model_id: DataModelId,
        view_repository: Optional[ViewRepository] = None,
    ) -> None:
        self._cognite_client = cognite_client
        self._data_model_id = data_model_id
        self._view_repository = view_repository

    def _get_msdp_data(self,
                       reporting_line_external_ids: Optional[list[str]] = None,
                       ):


        if reporting_line_external_ids:
            filter_field = "refReportingLine"
            variable_name = "reportingLines"
            external_ids = reporting_line_external_ids
        else:
            raise ValueError("Empty Params:  reporting_line_external_ids.")

        query_result = self._cognite_client.data_modeling.graphql.query(
            self._data_model_id,
            self.build_query(filter_field, variable_name),
            {variable_name: external_ids},
        )["listOEEMSDP"]["items"]

        if not query_result:
            return None

        return [
            Msdp.from_cognite_response(entry)
            for entry in query_result
        ]

    def get_msdp_data_as_dataframe(
        self,
        reporting_line_external_ids: Optional[list[str]] = None,
    ) -> pd.DataFrame:
        data = self._get_msdp_data(reporting_line_external_ids)

        if not data:
            return data

        entries: list[dict[str, Any]] = []
        for item in data:
            entry = item.model_dump(by_alias=True)
            if item.reporting_line:
                entry.update(
                    {
                        "reportingLineExternalId": item.reporting_line.external_id,
                        "reportingLineName": item.reporting_line.name,
                    }
                )
                entries.append(entry)
        table_raw = pd.DataFrame(entries)

        return table_raw

    def build_query(self, filter_field: str, variable_name: str) -> str:
        return f"""
            query QueryMsdp(${variable_name}: [ID!]) {{
                listOEEMSDP(
                    first: 1000
                    filter: {{{filter_field}: {{externalId: {{in: ${variable_name}}}}}}}
                    sort: {{effectiveDate: DESC}}
                ) {{
                    items {{
                        msdp
                        mssr
                        modifiedBy
                        lastUpdatedTime
                        externalId
                        effectiveDate
                        createdTime
                        createdBy
                        productGroup
                        piTagValue
                        refMaterial {{
                            externalId
                            space
                            name
                            description
                        }}
                        refSite {{
                            externalId
                            space
                        }}
                        refReportingLine {{
                            name
                            externalId
                            space
                        }}
                        scheduledRate
                        space
                        uom {{
                            name
                            externalId
                            space
                        }}
                    }}
                }}
            }}
        """


    def create_msdp(self, msdps: list[Msdp]) -> None:
        if not msdps:
            return

        if not self._view_repository:
            raise ValueError(
                "Missing View Repository when instantiating the MSDP Repository."
            )

        view = self._view_repository.get_view_id("OEEMSDP")

        keys_to_exclude = [
            "day",
            "month",
            "year",
            "index",
            "reportingBusinessExternalId",
            "businessSegmentSpace",
            "reportingSiteExternalId",
            "reportingSiteSpace",
            "geoRegionExternalId",
            "geoRegionSpace",
            "countryExternalId",
            "countrySpace",
            "reportingUnitExternalId",
            "reportingUnitSpace",
            "reportingLineExternalId",
            "reportingLineSpace",
            "uomExternalId",
            "uomSpace",
            "effective_date_datetime",
            'date_set_datetime'
        ]
        nodes = []
        for msdp in msdps:
            nodes.append(
                msdp.convert_to_cognite_node(
                    view, keys_to_exclude=keys_to_exclude
                )
            )

        paginated_nodes = [
            nodes[1000 * i : 1000 * (i + 1)]
            for i in range(int(len(nodes) / 1000) + 1)
        ]

        for entries in paginated_nodes:
            self._cognite_client.data_modeling.instances.apply(nodes=entries)
